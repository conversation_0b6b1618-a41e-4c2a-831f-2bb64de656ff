{"editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "javascript.preferences.importModuleSpecifier": "project-relative", "typescript.suggest.jsdoc.generateReturns": false, "typescript.tsserver.experimental.enableProjectDiagnostics": true, "typescript.tsdk": "node_modules/typescript/lib", "eslint.packageManager": "pnpm", "stylelint.packageManager": "pnpm", "npm.packageManager": "pnpm"}