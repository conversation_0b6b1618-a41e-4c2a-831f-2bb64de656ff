import { isNil, toNumber } from 'lodash';

import { Configure } from '../config/configure';

import { ConfigureFactory, ConfigureRegister } from '../config/types';

import { getRandomString, toBoolean } from './helpers';
import { AppConfig } from './types';

export const getDefaultAppConfig = (configure: Configure) => ({
    name: configure.env.get('APP_NAME', getRandomString()),
    host: configure.env.get('APP_HOST', '127.0.0.1'),
    port: configure.env.get('APP_PORT', (v) => toNumber(v), 3000),
    https: configure.env.get('APP_SSL', (v) => toBoolean(v), false),
    locale: configure.env.get('APP_LOCALE', 'zh_CN'),
    fallbackLocale: configure.env.get('APP_FALLBACK_LOCALE', 'en'),
    timezone: configure.env.get('APP_TIMEZONE', 'Asia/Shanghai'),
});

export const createAppConfig: (
    register: ConfigureRegister<RePartial<AppConfig>>,
) => ConfigureFactory<AppConfig> = (register) => ({
    register,
    defaultRegister: (configure) => getDefaultAppConfig(configure),
    hook: (configure: Configure, value) => {
        if (isNil(value.url)) {
            value.url = `${value.https ? 'https:' : 'http:'}//${value.host}:${value.port}`;
        }
        return value;
    },
});
