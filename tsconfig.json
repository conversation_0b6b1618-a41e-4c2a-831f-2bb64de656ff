{"compilerOptions": {"strict": true, "alwaysStrict": true, "target": "esnext", "module": "commonjs", "moduleResolution": "Node", "declaration": true, "declarationMap": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "isolatedModules": true, "esModuleInterop": true, "noUnusedLocals": true, "noImplicitReturns": true, "pretty": true, "resolveJsonModule": true, "allowJs": true, "importsNotUsedAsValues": "remove", "noEmit": false, "lib": ["esnext", "DOM", "ScriptHost", "WebWorker"], "baseUrl": ".", "outDir": "./dist", "types": ["bun-types", "@types/jest"], "paths": {"@/*": ["./src/*"]}}, "include": ["src", "test", "typings/**/*.d.ts", "**.js"]}